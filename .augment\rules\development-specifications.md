---
type: "always_apply"
description: "Example description"
---
# 开发规范

## 使用场景
- 在进行页面跳转操作时
- 需要获取URL参数时
- 开发新页面或修改现有页面时

## 关键规则

### 页面跳转规则
- 必须使用 `Tool.goPage` 方法进行所有页面跳转，禁止使用原生方法
- 根据跳转需求选择正确的方法：push、tab、back、replace、reLaunch
- 跳转函数已自动处理 storeId 参数，无需手动添加

### URL参数获取规则
- 必须使用 `Tool.getRoute` 方法获取所有URL参数，禁止直接解析URL
- 使用 `Tool.getRoute.params()` 获取所有参数
- 使用 `Tool.getRoute.path()` 获取当前页面路径

## 详细说明

### 页面跳转方法
- `Tool.goPage.push(path)`: 保留当前页面，跳转到应用内的某个页面
- `Tool.goPage.tab(tabName)`: 跳转到 tabBar 页面
- `Tool.goPage.back()`: 关闭当前页面，返回上一页面
- `Tool.goPage.replace(path)`: 关闭当前页面，跳转到应用内的某个页面
- `Tool.goPage.reLaunch(path)`: 关闭所有页面，打开应用内某个页面

### URL参数获取方法
- `Tool.getRoute.params()`: 获取当前页面的所有URL参数，返回一个包含所有参数的对象
- `Tool.getRoute.path()`: 获取当前页面的路径，不包含参数部分

## 示例

<example>
// 正确的页面跳转
import { Tool } from "path/to/utils/tools";

const goToPage = () => {
  Tool.goPage.push('/pages/example/example');
};

// 正确的参数获取
const getAllParams = () => {
  const params = Tool.getRoute.params();
  const storeId = params.storeId;
  return params;
};
</example>

<example type="invalid">
// 错误的页面跳转
const wrongJump = () => {
  uni.navigateTo({ url: '/pages/example/example' });  // 禁止使用
};

// 错误的参数获取
const wrongGetParams = () => {
  const url = window.location.href;
  const params = new URLSearchParams(url.split('?')[1]);  // 禁止使用
  return params.get('storeId');
};
</example>

## 注意事项
- 跳转函数已经处理了 storeId 参数，无需手动添加
- 尽量避免在跳转时使用复杂动画或多层 setTimeout
- 跳转函数会自动处理跳转失败的情况
- `Tool.getRoute.params()` 会自动处理参数解码
- 参数获取方法已处理各种边缘情况，确保稳定性
- 统一使用这些方法可确保代码的一致性和可维护性

