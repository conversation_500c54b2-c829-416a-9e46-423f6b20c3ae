# UI静态页面构建规则

## 使用场景
- 根据用户提供的UI图、HTML、CSS在指定页面添加静态UI界面
- 开发新页面或组件的布局和样式
- 需要将外部UI资源转换为项目内部统一规范的场景

## 关键规则
- **严守视觉还原**：严格按照UI图或参考代码进行视觉还原，但应以实现布局意图为核心。
- **禁止机械复刻**：**严禁**机械式地复制UI设计工具（如蓝湖）生成的DOM结构和布局代码（如 `margin`, `position`）。
- **理解并重构**：必须分析并理解设计的DOM结构和布局意图（如居中、两端对齐等），并使用语义化标签和 `Flexbox` 等现代CSS技术进行重构。
- **语义化命名**：在理解和重构布局后，必须对CSS类名进行有意义的命名，以反映其功能或内容，避免使用如 `box_1`、`group_2` 等无意义名称。
- **统一单位规范**：所有尺寸单位必须使用 `rpx` 替代 `px`，保持数值不变。
- **代码结构清晰**：采用语义化标签，样式使用SCSS嵌套结构，并遵循Vue组件化开发规范。

## 详细指南

### 单位转换规则
- 直接将所有px单位替换为rpx，保持原始数值不变
- 不需要进行任何数值计算或转换
- 百分比、em、rem等其他单位保持原样不变

### 代码组织结构
- 将HTML结构放置在template标签内
- 将样式代码放置在style标签内，并添加scoped和lang="scss"属性
- 组件逻辑放置在script标签内

### 布局实现原则
- **意图驱动开发**：在编码前，必须分析UI元素间的对齐与分布关系。UI工具导出的代码仅作为视觉与尺寸参考，其布局实现方式（通常是 `margin` 或 `position`）必须被更优的方式替代。
- **Flexbox优先**：对于一维布局（元素沿水平或垂直方向排列），**必须**优先使用Flexbox。通过 `justify-content`, `align-items`, 和 `gap` 属性控制主轴和交叉轴的对齐与间距。
- **合理使用Margin**：`margin` 属性应主要用于元素与元素之间的微调间距，**严禁**使用大数值的 `margin` 来实现页面宏观布局定位。
- **复杂布局**：对于复杂的界面，应通过嵌套的Flexbox/Grid容器来构建，以保持布局的清晰和可扩展性。

### 视觉还原原则
- 保持边距、内边距、字体大小、行高等与UI图一致
- 保持颜色、阴影、圆角等视觉效果与UI图一致
- 响应式布局应根据原始设计适配

## 示例
<example>
<!-- 正确的页面布局实现 -->
<template>
  <div class="product-card">
    <div class="product-image">
      <img src="/static/image/product.png" alt="产品图片">
    </div>
    <div class="product-info">
      <h3 class="product-title">产品名称</h3>
      <div class="product-price">¥99.00</div>
      <div class="product-actions">
        <button class="buy-btn">立即购买</button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.product-card {
  width: 320rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .product-image {
    height: 200rpx;
    margin-bottom: 12rpx;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .product-info {
    .product-title {
      font-size: 16rpx;
      margin-bottom: 8rpx;
    }
    
    .product-price {
      font-size: 18rpx;
      color: #ff6700;
      margin-bottom: 12rpx;
    }
    
    .product-actions {
      .buy-btn {
        width: 100%;
        height: 40rpx;
        background-color: #ff6700;
        color: #fff;
        border: none;
        border-radius: 4rpx;
      }
    }
  }
}
</style>
</example>

<example type="invalid">
<!-- 错误的页面布局实现 -->
<template>
  <div class="c1">
    <div class="c2">
      <img class="c3" src="/static/image/product.png">
    </div>
    <div class="c4">
      <h3 class="c5">产品名称</h3>
      <div class="c6">¥99.00</div>
      <div class="c7">
        <button class="c8">立即购买</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.c1 {
  width: 320px; /* 错误：使用了px单位而非rpx */
  padding: 16px; /* 错误：使用了px单位而非rpx */
}
.c2 {
  height: 200px; /* 错误：使用了px单位而非rpx */
}
.c3 {
  width: 100%;
}
.c5 {
  font-size: 1rem; /* 错误：使用了rem而非rpx */
}
.c6 {
  font-size: 18px; /* 错误：使用了px单位而非rpx */
}
.c8 {
  width: 100%;
  height: 40px; /* 错误：使用了px单位而非rpx */
  background-color: #ff6700;
  color: #fff;
}
</style>
</example>

